use async_nats::Client;
use futures_util::StreamExt;
use serde_json::json;
use std::time::{SystemTime, UNIX_EPOCH};
use std::collections::HashMap;

use basilisk_bot::shared_types::{BlockPropagationSample, NetworkResonanceState, NatsTopics};
use basilisk_bot::config::Settings;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let nats_client = async_nats::connect("nats://localhost:4222").await?;

    // Load configuration
    let settings = Settings::new(None)?;

    let mut subscriber = nats_client
        .subscribe(NatsTopics::DATA_NETWORK_PROPAGATION)
        .await?;

    println!("Seismic Analyzer listening on {}", NatsTopics::DATA_NETWORK_PROPAGATION);

    while let Some(message) = subscriber.next().await {
        let sample: BlockPropagationSample = serde_json::from_slice(&message.payload)?;
        
        // AUDIT-FIX: Implement proper P-Wave/S-Wave analysis logic
        let (sp_time_ms, network_coherence_score, is_shock_event, sp_time_20th_percentile) =
            analyze_propagation_sample(&sample, &settings);

        let resonance_state = NetworkResonanceState {
            sp_time_ms,
            network_coherence_score,
            is_shock_event,
            sp_time_20th_percentile, // AUDIT-FIX: Use actual 20th percentile calculation
            sequencer_status: determine_sequencer_status(&sample), // AUDIT-FIX: Dynamic status
            censorship_detected: detect_censorship(&sample), // AUDIT-FIX: Actual censorship detection
        };

        let payload = serde_json::to_vec(&resonance_state)?;
        nats_client.publish(NatsTopics::STATE_NETWORK_RESONANCE, payload.into()).await?;
    }

    Ok(())
}

/// AUDIT-FIX: Enhanced propagation analysis with proper percentile calculations
fn analyze_propagation_sample(sample: &BlockPropagationSample, settings: &Settings) -> (f64, f64, bool, f64) {
    if sample.samples.len() < 2 {
        return (0.0, 0.0, false, 0.0);
    }

    // Sort samples by timestamp to find P-wave and S-wave
    let mut sorted_samples = sample.samples.clone();
    sorted_samples.sort_by_key(|s| s.1);

    let p_wave_time = sorted_samples[0].1; // First reported timestamp

    // S-wave: 80th percentile of reported timestamps
    let s_wave_index = ((sorted_samples.len() as f64) * 0.8) as usize;
    let s_wave_time = sorted_samples[s_wave_index].1;

    // AUDIT-FIX: Calculate actual 20th percentile
    let percentile_20_index = ((sorted_samples.len() as f64) * 0.2) as usize;
    let percentile_20_time = sorted_samples[percentile_20_index].1;
    let sp_time_20th_percentile = ((percentile_20_time - p_wave_time) as f64) / 1_000_000.0;

    let sp_time_ms = ((s_wave_time - p_wave_time) as f64) / 1_000_000.0; // Convert nanos to millis

    // AUDIT-FIX: Improved coherence score calculation
    let mean_time = sorted_samples.iter().map(|s| s.1 as f64).sum::<f64>() / sorted_samples.len() as f64;
    let variance = sorted_samples.iter().map(|s| (s.1 as f64 - mean_time).powi(2)).sum::<f64>() / sorted_samples.len() as f64;
    let std_dev = variance.sqrt();

    // Normalize coherence score to [0,1] range with better scaling
    let network_coherence_score = if std_dev > 0.0 {
        1.0 / (1.0 + (std_dev / 10_000_000.0)) // Adjusted scaling factor
    } else {
        1.0
    };

    // Shock event detection using configurable threshold
    let is_shock_event = sp_time_ms > settings.execution.network_shock_threshold_ms as f64;

    (sp_time_ms, network_coherence_score, is_shock_event, sp_time_20th_percentile)
}

/// AUDIT-FIX: Dynamic sequencer status determination
fn determine_sequencer_status(sample: &BlockPropagationSample) -> String {
    if sample.samples.len() < 3 {
        return "Insufficient Data".to_string();
    }

    // Sort by timestamp to analyze propagation pattern
    let mut sorted_samples = sample.samples.clone();
    sorted_samples.sort_by_key(|s| s.1);

    let p_wave_time = sorted_samples[0].1;
    let last_time = sorted_samples.last().unwrap().1;
    let total_propagation_ms = ((last_time - p_wave_time) as f64) / 1_000_000.0;

    // Determine status based on propagation characteristics
    if total_propagation_ms < 100.0 {
        "Excellent".to_string()
    } else if total_propagation_ms < 300.0 {
        "Healthy".to_string()
    } else if total_propagation_ms < 1000.0 {
        "Degraded".to_string()
    } else {
        "Poor".to_string()
    }
}

/// AUDIT-FIX: Basic censorship detection implementation
fn detect_censorship(sample: &BlockPropagationSample) -> bool {
    // Simple heuristic: if propagation is unusually slow or uneven, might indicate censorship
    if sample.samples.len() < 5 {
        return false; // Not enough data to determine
    }

    let mut sorted_samples = sample.samples.clone();
    sorted_samples.sort_by_key(|s| s.1);

    let p_wave_time = sorted_samples[0].1;
    let median_index = sorted_samples.len() / 2;
    let median_time = sorted_samples[median_index].1;

    let median_propagation_ms = ((median_time - p_wave_time) as f64) / 1_000_000.0;

    // If median propagation time is > 2 seconds, consider it potential censorship
    median_propagation_ms > 2000.0
}
