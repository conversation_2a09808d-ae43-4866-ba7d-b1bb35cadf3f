// MISSION: CircuitBreaker - Risk Management and Trading Halt Logic
// WHY: Prevent catastrophic losses by automatically halting trading when risk thresholds are exceeded
// HOW: Track daily PnL and implement configurable circuit breaker logic with atomic state management

use anyhow::Result;
use chrono::{DateTime, Utc, Duration};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use std::sync::{Arc, Mutex, atomic::{AtomicBool, Ordering}};
use tracing::{debug, info, warn, error};
use crate::error::BasiliskError;
use async_nats::Client as NatsClient;

/// Configuration for circuit breaker behavior
#[derive(Debug, Clone)]
pub struct CircuitBreakerConfig {
    /// Maximum daily loss before halting trading (in USD)
    pub max_daily_loss_usd: Decimal,
    /// Maximum consecutive losses before halting
    pub max_consecutive_losses: u32,
    /// Maximum loss percentage of total capital
    pub max_loss_percentage: Decimal,
    /// Time window for loss calculation (in hours)
    pub loss_window_hours: i64,
    /// Minimum time between trades (in seconds) to prevent rapid-fire losses
    pub min_trade_interval_seconds: u64,
    /// Auto-reset circuit breaker after this many hours
    pub auto_reset_hours: Option<i64>,
    /// Require manual operator confirmation for reset
    pub manual_reset_required: bool,
    /// List of authorized operator addresses for manual reset
    pub authorized_operators: Vec<String>,
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self {
            max_daily_loss_usd: dec!(500.0), // $500 max daily loss
            max_consecutive_losses: 5, // 5 consecutive losses
            max_loss_percentage: dec!(0.10), // 10% of capital
            loss_window_hours: 24, // 24 hour window
            min_trade_interval_seconds: 30, // 30 seconds between trades
            auto_reset_hours: Some(24), // Auto-reset after 24 hours
            manual_reset_required: true, // Require manual confirmation for production safety
            authorized_operators: vec![], // Empty by default - must be configured
        }
    }
}

/// Trade result for PnL tracking
#[derive(Debug, Clone)]
pub struct TradeResult {
    pub trade_id: String,
    pub timestamp: DateTime<Utc>,
    pub pnl_usd: Decimal,
    pub is_profitable: bool,
}

/// Internal state for circuit breaker
#[derive(Debug, Clone)]
struct CircuitBreakerState {
    /// Current daily PnL (can be negative)
    daily_pnl_usd: Decimal,
    /// Number of consecutive losses
    consecutive_losses: u32,
    /// Recent trade results within the loss window
    recent_trades: Vec<TradeResult>,
    /// Timestamp when circuit breaker was last triggered
    last_halt_time: Option<DateTime<Utc>>,
    /// Timestamp of last trade
    last_trade_time: Option<DateTime<Utc>>,
    /// Total number of halts today
    halt_count_today: u32,
}

impl Default for CircuitBreakerState {
    fn default() -> Self {
        Self {
            daily_pnl_usd: dec!(0.0),
            consecutive_losses: 0,
            recent_trades: Vec::new(),
            last_halt_time: None,
            last_trade_time: None,
            halt_count_today: 0,
        }
    }
}

/// Circuit breaker for risk management and trading halt logic
pub struct CircuitBreaker {
    config: CircuitBreakerConfig,
    state: Arc<Mutex<CircuitBreakerState>>,
    is_halted: Arc<AtomicBool>,
    total_capital_usd: Arc<Mutex<Decimal>>,
    // MEDIUM PRIORITY FIX #1: Alerting Integration
    nats_client: Option<NatsClient>,
}

impl CircuitBreaker {
    /// Create a new CircuitBreaker
    pub fn new(config: CircuitBreakerConfig, initial_capital_usd: Decimal) -> Self {
        info!(
            "CircuitBreaker initialized - Max daily loss: ${}, Max consecutive losses: {}, Capital: ${}",
            config.max_daily_loss_usd,
            config.max_consecutive_losses,
            initial_capital_usd
        );
        
        Self {
            config,
            state: Arc::new(Mutex::new(CircuitBreakerState::default())),
            is_halted: Arc::new(AtomicBool::new(false)),
            total_capital_usd: Arc::new(Mutex::new(initial_capital_usd)),
            nats_client: None,
        }
    }
    
    /// Create a new CircuitBreaker with alerting integration
    pub fn new_with_alerting(
        config: CircuitBreakerConfig, 
        initial_capital_usd: Decimal,
        nats_client: NatsClient,
    ) -> Self {
        info!(
            "CircuitBreaker initialized with alerting - Max daily loss: ${}, Max consecutive losses: {}, Capital: ${}",
            config.max_daily_loss_usd,
            config.max_consecutive_losses,
            initial_capital_usd
        );
        
        Self {
            config,
            state: Arc::new(Mutex::new(CircuitBreakerState::default())),
            is_halted: Arc::new(AtomicBool::new(false)),
            total_capital_usd: Arc::new(Mutex::new(initial_capital_usd)),
            nats_client: Some(nats_client),
        }
    }
    
    /// Check if trading is currently halted
    pub fn is_trading_halted(&self) -> bool {
        let is_halted = self.is_halted.load(Ordering::Relaxed);
        
        // Check for auto-reset if configured and manual reset not required
        if is_halted && !self.config.manual_reset_required {
            self.check_auto_reset();
        }
        
        self.is_halted.load(Ordering::Relaxed)
    }
    
    /// Check for auto-reset conditions and reset if applicable
    /// Returns true if reset was performed
    pub fn check_auto_reset(&self) -> bool {
        if let Some(auto_reset_hours) = self.config.auto_reset_hours {
            if let Ok(state) = self.state.lock() {
                if let Some(last_halt_time) = state.last_halt_time {
                    let time_since_halt = Utc::now() - last_halt_time;
                    if time_since_halt > Duration::hours(auto_reset_hours) {
                        info!("Auto-resetting circuit breaker after {} hours", auto_reset_hours);
                        drop(state); // Release lock before calling reset
                        let _ = self.reset();
                        return true;
                    }
                }
            }
        }
        false
    }
    
    /// Check conditions and update state with a new trade result
    /// Returns Ok(()) if trading should continue, Err if trading should be halted
    pub fn check_and_update(&self, trade_pnl_usd: Decimal) -> Result<()> {
        let mut state = self.state.lock().map_err(|e| {
            anyhow::anyhow!("Failed to acquire circuit breaker state lock: {}", e)
        })?;
        
        let now = Utc::now();
        let trade_result = TradeResult {
            trade_id: format!("trade_{}", now.timestamp()),
            timestamp: now,
            pnl_usd: trade_pnl_usd,
            is_profitable: trade_pnl_usd > dec!(0.0),
        };
        
        info!(
            "CircuitBreaker: Processing trade result - PnL: ${}, Profitable: {}",
            trade_pnl_usd,
            trade_result.is_profitable
        );
        
        // Check minimum trade interval
        if let Some(last_trade_time) = state.last_trade_time {
            let time_since_last_trade = now - last_trade_time;
            if time_since_last_trade.num_seconds() < self.config.min_trade_interval_seconds as i64 {
                return Err(anyhow::anyhow!(
                    "Trade interval too short: {} seconds (minimum: {} seconds)",
                    time_since_last_trade.num_seconds(),
                    self.config.min_trade_interval_seconds
                ));
            }
        }
        
        // Update state
        state.last_trade_time = Some(now);
        state.daily_pnl_usd += trade_pnl_usd;
        state.recent_trades.push(trade_result.clone());
        
        // Clean up old trades outside the loss window
        let cutoff_time = now - Duration::hours(self.config.loss_window_hours);
        state.recent_trades.retain(|trade| trade.timestamp > cutoff_time);
        
        // Update consecutive losses counter
        if trade_result.is_profitable {
            state.consecutive_losses = 0;
        } else {
            state.consecutive_losses += 1;
        }
        
        // Check circuit breaker conditions
        let mut halt_reasons = Vec::new();
        
        // 1. Check daily loss limit
        if state.daily_pnl_usd < -self.config.max_daily_loss_usd {
            halt_reasons.push(format!(
                "Daily loss ${:.2} exceeds limit ${:.2}",
                -state.daily_pnl_usd,
                self.config.max_daily_loss_usd
            ));
        }
        
        // 2. Check consecutive losses
        if state.consecutive_losses >= self.config.max_consecutive_losses {
            halt_reasons.push(format!(
                "Consecutive losses {} exceeds limit {}",
                state.consecutive_losses,
                self.config.max_consecutive_losses
            ));
        }
        
        // 3. Check loss percentage of capital
        if let Ok(capital) = self.total_capital_usd.lock() {
            let loss_percentage = (-state.daily_pnl_usd) / *capital;
            if loss_percentage > self.config.max_loss_percentage {
                halt_reasons.push(format!(
                    "Loss percentage {:.2}% exceeds limit {:.2}%",
                    loss_percentage * dec!(100.0),
                    self.config.max_loss_percentage * dec!(100.0)
                ));
            }
        }
        
        // If any halt conditions are met, trigger circuit breaker
        if !halt_reasons.is_empty() {
            state.last_halt_time = Some(now);
            state.halt_count_today += 1;
            
            self.is_halted.store(true, Ordering::Relaxed);
            
            error!(
                "CIRCUIT BREAKER TRIGGERED - Reasons: {:?}",
                halt_reasons
            );
            error!(
                "Trading halted - Daily PnL: ${:.2}, Consecutive losses: {}, Halt count today: {}",
                state.daily_pnl_usd,
                state.consecutive_losses,
                state.halt_count_today
            );
            
            // MEDIUM PRIORITY FIX #1: Implement Alerting Integration
            // Note: send_critical_alert is async but this context is sync
            // TODO: Implement proper async context or use tokio::spawn
            
            return Err(anyhow::anyhow!(
                "Circuit breaker triggered: {}",
                halt_reasons.join(", ")
            ));
        }
        
        debug!(
            "CircuitBreaker: Trade processed successfully - Daily PnL: ${:.2}, Consecutive losses: {}",
            state.daily_pnl_usd,
            state.consecutive_losses
        );
        
        Ok(())
    }
    
    /// Manually reset the circuit breaker (admin function)
    pub fn reset(&self) -> Result<()> {
        let mut state = self.state.lock().map_err(|e| {
            anyhow::anyhow!("Failed to acquire circuit breaker state lock: {}", e)
        })?;
        
        info!("Manually resetting circuit breaker");
        
        // Reset state but keep trade history for analysis
        state.consecutive_losses = 0;
        state.last_halt_time = None;
        
        // Reset atomic halt flag
        self.is_halted.store(false, Ordering::Relaxed);
        
        info!("Circuit breaker reset - Trading resumed");
        
        Ok(())
    }
    
    /// Get current circuit breaker status for monitoring
    pub fn get_status(&self) -> Result<CircuitBreakerStatus> {
        let state = self.state.lock().map_err(|e| {
            anyhow::anyhow!("Failed to acquire circuit breaker state lock: {}", e)
        })?;
        
        let capital = self.total_capital_usd.lock().map_err(|e| {
            anyhow::anyhow!("Failed to acquire capital lock: {}", e)
        })?;
        
        Ok(CircuitBreakerStatus {
            is_halted: self.is_trading_halted(),
            daily_pnl_usd: state.daily_pnl_usd,
            consecutive_losses: state.consecutive_losses,
            recent_trade_count: state.recent_trades.len(),
            last_halt_time: state.last_halt_time,
            halt_count_today: state.halt_count_today,
            total_capital_usd: *capital,
            loss_percentage: if *capital > dec!(0.0) {
                (-state.daily_pnl_usd) / *capital
            } else {
                dec!(0.0)
            },
        })
    }
    
    /// Update total capital (for percentage-based calculations)
    pub fn update_capital(&self, new_capital_usd: Decimal) -> Result<()> {
        let mut capital = self.total_capital_usd.lock().map_err(|e| {
            anyhow::anyhow!("Failed to acquire capital lock: {}", e)
        })?;
        
        info!("Updating total capital from ${} to ${}", *capital, new_capital_usd);
        *capital = new_capital_usd;
        
        Ok(())
    }
    
    /// Force halt trading (emergency function)
    pub fn emergency_halt(&self, reason: String) -> Result<()> {
        let mut state = self.state.lock().map_err(|e| {
            anyhow::anyhow!("Failed to acquire circuit breaker state lock: {}", e)
        })?;
        
        error!("🚨 EMERGENCY HALT TRIGGERED 🚨 - Reason: {}", reason);
        
        state.last_halt_time = Some(Utc::now());
        state.halt_count_today += 1;
        
        self.is_halted.store(true, Ordering::Relaxed);
        
        Ok(())
    }
    
    /// Get recent trade history
    pub fn get_recent_trades(&self) -> Result<Vec<TradeResult>> {
        let state = self.state.lock().map_err(|e| {
            anyhow::anyhow!("Failed to acquire circuit breaker state lock: {}", e)
        })?;
        
        Ok(state.recent_trades.clone())
    }
    
    /// Manual reset with operator authorization - PRODUCTION CRITICAL
    pub async fn manual_reset(&self, operator_signature: &str) -> Result<()> {
        info!("Manual circuit breaker reset requested by operator: {}", operator_signature);
        
        // Validate operator authorization
        if !self.config.authorized_operators.contains(&operator_signature.to_string()) {
            error!("Unauthorized reset attempt by operator: {}", operator_signature);
            return Err(anyhow::anyhow!("Operator not authorized for manual reset: {}", operator_signature));
        }
        
        // Get halt reason for logging
        let halt_reason = self.get_last_halt_reason();
        
        // Perform reset
        self.reset()?;
        
        // Send reset notification
        if let Some(nats_client) = &self.nats_client {
            let reset_alert = serde_json::json!({
                "type": "CIRCUIT_BREAKER_RESET",
                "severity": "INFO", 
                "timestamp": chrono::Utc::now(),
                "operator": operator_signature,
                "previous_halt_reason": halt_reason,
                "reset_method": "manual"
            });
            
            if let Err(e) = nats_client.publish(
                crate::shared_types::NatsTopics::ALERTS_OPERATIONAL, 
                reset_alert.to_string().into()
            ).await {
                warn!("Failed to send reset notification: {}", e);
            } else {
                info!("Circuit breaker reset notification sent successfully");
            }
        }
        
        info!("Circuit breaker manually reset by operator: {}", operator_signature);
        Ok(())
    }
    
    /// Get the reason for the last halt (for logging and notifications)
    pub fn get_last_halt_reason(&self) -> String {
        if let Ok(state) = self.state.lock() {
            if state.last_halt_time.is_some() {
                let mut reasons = Vec::new();
                
                if state.daily_pnl_usd < -self.config.max_daily_loss_usd {
                    reasons.push(format!("Daily loss ${:.2} exceeded limit ${:.2}", 
                                       -state.daily_pnl_usd, self.config.max_daily_loss_usd));
                }
                
                if state.consecutive_losses >= self.config.max_consecutive_losses {
                    reasons.push(format!("Consecutive losses {} exceeded limit {}", 
                                       state.consecutive_losses, self.config.max_consecutive_losses));
                }
                
                if !reasons.is_empty() {
                    return reasons.join("; ");
                }
            }
        }
        "Unknown halt reason".to_string()
    }
    
    /// MEDIUM PRIORITY FIX #1: Send critical alert when circuit breaker trips
    async fn send_critical_alert(&self, halt_reasons: &[String], state: &CircuitBreakerState) {
        if let Some(nats_client) = &self.nats_client {
            let alert = serde_json::json!({
                "type": "CIRCUIT_BREAKER_TRIGGERED",
                "severity": "CRITICAL",
                "timestamp": chrono::Utc::now(),
                "reasons": halt_reasons,
                "daily_pnl_usd": state.daily_pnl_usd,
                "consecutive_losses": state.consecutive_losses,
                "halt_count_today": state.halt_count_today,
                "requires_immediate_attention": true
            });
            
            if let Err(e) = nats_client
                .publish(crate::shared_types::NatsTopics::ALERTS_CRITICAL_CIRCUIT_BREAKER, alert.to_string().into())
                .await
            {
                error!("Failed to send circuit breaker alert: {}", e);
            } else {
                info!("Critical circuit breaker alert sent successfully");
            }
        } else {
            warn!("No NATS client available for circuit breaker alerting");
        }
    }
}

/// Status information for monitoring
#[derive(Debug, Clone)]
pub struct CircuitBreakerStatus {
    pub is_halted: bool,
    pub daily_pnl_usd: Decimal,
    pub consecutive_losses: u32,
    pub recent_trade_count: usize,
    pub last_halt_time: Option<DateTime<Utc>>,
    pub halt_count_today: u32,
    pub total_capital_usd: Decimal,
    pub loss_percentage: Decimal,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_circuit_breaker_creation() {
        let config = CircuitBreakerConfig::default();
        let circuit_breaker = CircuitBreaker::new(config, dec!(1000.0));
        
        assert!(!circuit_breaker.is_halted.load(std::sync::atomic::Ordering::Relaxed));
        
        let status = circuit_breaker.get_status()
            .expect("Circuit breaker should provide status in test");
        assert_eq!(status.daily_pnl_usd, dec!(0.0));
        assert_eq!(status.consecutive_losses, 0);
        assert_eq!(status.total_capital_usd, dec!(1000.0));
    }
    
    #[test]
    fn test_daily_loss_limit() {
        let config = CircuitBreakerConfig {
            max_daily_loss_usd: dec!(100.0),
            ..Default::default()
        };
        let circuit_breaker = CircuitBreaker::new(config, dec!(1000.0));
        
        // First loss should be fine
        assert!(circuit_breaker.check_and_update(dec!(-50.0)).is_ok());
        assert!(!circuit_breaker.is_trading_halted());
        
        // Second loss should trigger circuit breaker
        assert!(circuit_breaker.check_and_update(dec!(-60.0)).is_err());
        assert!(circuit_breaker.is_trading_halted());
    }
    
    #[test]
    fn test_consecutive_losses() {
        let config = CircuitBreakerConfig {
            max_consecutive_losses: 3,
            max_daily_loss_usd: dec!(1000.0), // High limit to test consecutive losses
            ..Default::default()
        };
        let circuit_breaker = CircuitBreaker::new(config, dec!(1000.0));
        
        // First two losses should be fine
        assert!(circuit_breaker.check_and_update(dec!(-10.0)).is_ok());
        assert!(circuit_breaker.check_and_update(dec!(-10.0)).is_ok());
        assert!(!circuit_breaker.is_trading_halted());
        
        // Third consecutive loss should trigger circuit breaker
        assert!(circuit_breaker.check_and_update(dec!(-10.0)).is_err());
        assert!(circuit_breaker.is_trading_halted());
    }
    
    #[test]
    fn test_profitable_trade_resets_consecutive_losses() {
        let config = CircuitBreakerConfig {
            max_consecutive_losses: 3,
            max_daily_loss_usd: dec!(1000.0),
            ..Default::default()
        };
        let circuit_breaker = CircuitBreaker::new(config, dec!(1000.0));
        
        // Two losses
        assert!(circuit_breaker.check_and_update(dec!(-10.0)).is_ok());
        assert!(circuit_breaker.check_and_update(dec!(-10.0)).is_ok());
        
        // Profitable trade should reset consecutive losses
        assert!(circuit_breaker.check_and_update(dec!(50.0)).is_ok());
        
        // Two more losses should be fine (consecutive counter was reset)
        assert!(circuit_breaker.check_and_update(dec!(-10.0)).is_ok());
        assert!(circuit_breaker.check_and_update(dec!(-10.0)).is_ok());
        assert!(!circuit_breaker.is_trading_halted());
    }
    
    #[test]
    fn test_reset_functionality() {
        let config = CircuitBreakerConfig {
            max_daily_loss_usd: dec!(50.0),
            ..Default::default()
        };
        let circuit_breaker = CircuitBreaker::new(config, dec!(1000.0));
        
        // Trigger circuit breaker
        assert!(circuit_breaker.check_and_update(dec!(-60.0)).is_err());
        assert!(circuit_breaker.is_trading_halted());
        
        // Reset should allow trading again
        assert!(circuit_breaker.reset().is_ok());
        assert!(!circuit_breaker.is_trading_halted());
    }
}