// MISSION: Geometric Analysis Module for the Aetheric Resonance Engine
// WHY: Apply sacred geometry principles to understand market structure
// HOW: Mathematical functions based on Vesica Piscis and other geometric forms

use crate::shared_types::{ArbitragePath, ArbitragePool, GeometricScore, GeometricScorer, Pool};
use crate::data::oracle::PriceOracle;
use ethers::types::Address;

/// A simplified GeometryScorer implementation for the TUI
pub struct GeometryScorer {}

#[async_trait::async_trait]
impl GeometricScorer for GeometryScorer {
    async fn calculate_score(&self, _path: &ArbitragePath) -> anyhow::Result<GeometricScore> {
        // Return a default score for the TUI
        Ok(GeometricScore {
            convexity_ratio: rust_decimal::Decimal::new(5, 1), // 0.5
            liquidity_centroid_bias: rust_decimal::Decimal::new(5, 1), // 0.5
            harmonic_path_score: rust_decimal::Decimal::new(5, 1), // 0.5
        })
    }
}

use async_trait::async_trait;
use geo::{
    algorithm::convex_hull::ConvexHull,
    prelude::*,
    Point,
    Polygon,
    LineString,
    Coord,
    MultiPoint,
};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use num_traits::{FromPrimitive, ToPrimitive};
use tracing::debug;
use anyhow::{anyhow, Result};

/// Represents the geometric properties of a single pool in USD-space.
struct PoolGeometricData {
    /// The 2D point representing the pool's reserves in USD.
    point: Point,
    /// The total liquidity of the pool in USD.
    liquidity_usd: Decimal,
    /// The symbols of the two tokens in the pool.
    token_symbols: (String, String),
}



/// The production implementation of the GeometricScorer.
/// It calculates scores based on the geometric properties of liquidity pools.
pub struct RealGeometricScorer<O: PriceOracle> {
    /// A price oracle to convert token amounts to USD.
    price_oracle: O,
    /// Configuration for anchor assets (central market assets)
    pub anchor_assets: Vec<String>,
}

impl<O: PriceOracle + Send + Sync> RealGeometricScorer<O> {
    /// Create a new RealGeometricScorer with a price oracle and default anchor assets
    pub fn new(price_oracle: O) -> Self {
        Self {
            price_oracle,
            anchor_assets: vec![
                "WETH".to_string(),
                "USDC".to_string(),
                "USDT".to_string(),
                "DAI".to_string(),
                "WBTC".to_string(),
            ],
        }
    }

    /// Create a new RealGeometricScorer with a price oracle and custom anchor assets
    pub fn with_anchor_assets(price_oracle: O, anchor_assets: Vec<String>) -> Self {
        Self { price_oracle, anchor_assets }
    }

    /// Fetches and converts all necessary data for a path into a geometric format.
    async fn get_path_geometric_data(&self, path: &ArbitragePath) -> Result<Vec<PoolGeometricData>> {
        let mut data = Vec::with_capacity(path.len());
        for pool in path {
            let token_a_symbol = pool.token0_symbol.clone();
            let token_b_symbol = pool.token1_symbol.clone();

            // Get prices from oracle - convert string symbols to Address (this is a simplified approach)
            let token_a_address = Address::zero(); // In a real implementation, we would look up the address from the symbol
            let token_b_address = Address::zero(); // In a real implementation, we would look up the address from the symbol
            
            // AUDIT-FIX: Use default price of $1 instead of zero when price not found
            let token_a_price_usd = match self.price_oracle.get_price(token_a_address).await {
                Ok(price) => price,
                Err(_) => rust_decimal::Decimal::ONE, // Default to $1 instead of zero
            };
            let token_b_price_usd = match self.price_oracle.get_price(token_b_address).await {
                Ok(price) => price,
                Err(_) => rust_decimal::Decimal::ONE, // Default to $1 instead of zero
            };
            
            // Convert reserves to USD using price oracle
            let reserve_a_usd = pool.reserve0 * token_a_price_usd;
            let reserve_b_usd = pool.reserve1 * token_b_price_usd;
            
            data.push(PoolGeometricData {
                point: geo::Point::new(
                    reserve_a_usd.to_f64().ok_or_else(|| anyhow::anyhow!("Failed to convert reserve_a_usd to f64"))?,
                    reserve_b_usd.to_f64().ok_or_else(|| anyhow::anyhow!("Failed to convert reserve_b_usd to f64"))?
                ),
                liquidity_usd: reserve_a_usd + reserve_b_usd,
                token_symbols: (token_a_symbol, token_b_symbol),
            });
        }
        Ok(data)
    }
}


#[async_trait]
impl<O: PriceOracle + Send + Sync> GeometricScorer for RealGeometricScorer<O> {
    async fn calculate_score(&self, path: &ArbitragePath) -> Result<GeometricScore> {
        if path.len() < 3 {
            debug!("MANDORLA GAUGE: Path too short for geometric analysis ({})", path.len());
            return Ok(GeometricScore {
                convexity_ratio: dec!(0.5), // Neutral score
                liquidity_centroid_bias: dec!(0.5), // Neutral bias
                harmonic_path_score: dec!(0.5), // Neutral bias
            });
        }

        let geometric_data = self.get_path_geometric_data(path).await?;

        let convexity_ratio = self.calculate_convexity_ratio(&geometric_data)?;
        let harmonic_path_score = self.calculate_harmonic_path_score(&geometric_data)?;

        debug!(
            "MANDORLA GAUGE: Real Geometric Score | Convexity: {:.4} | Harmonic Path Score: {:.4}",
            convexity_ratio, harmonic_path_score
        );

        Ok(GeometricScore {
            convexity_ratio,
            liquidity_centroid_bias: self.calculate_liquidity_centroid_bias(&geometric_data, harmonic_path_score)?,
            harmonic_path_score,
        })
    }
}


impl<O: PriceOracle + Send + Sync> RealGeometricScorer<O> {
    /// Calculates the Convexity Ratio as defined in our research.
    /// This quantifies the "fullness" of an opportunity by comparing the area of the
    /// arbitrage path polygon to the area of its convex hull in USD-space.
    fn calculate_convexity_ratio(&self, data: &[PoolGeometricData]) -> Result<Decimal> {
        let points: Vec<Point> = data.iter().map(|d| d.point).collect();

        // 2. Calculate the convex hull of these points
        let multi_point = geo::MultiPoint(points.clone());
        let convex_hull_polygon: Polygon = multi_point.convex_hull();

        // 3. Calculate the area of the convex hull
        let hull_area_f64 = convex_hull_polygon.unsigned_area();
        if hull_area_f64 < 1e-9 {
            debug!("MANDORLA GAUGE: Degenerate convex hull, returning perfect convexity");
            return Ok(Decimal::ONE);
        }

        // 4. Create a polygon from the path (in order)
        let path_coords: Vec<Coord> = points.iter().map(|p| p.0).collect();
        
        let mut closed_coords = path_coords.clone();
        if !closed_coords.is_empty() && closed_coords.first() != closed_coords.last() {
            closed_coords.push(closed_coords[0]);
        }
        
        let path_linestring = LineString::new(closed_coords);
        let path_polygon = Polygon::new(path_linestring, vec![]);
        let path_area_f64 = path_polygon.unsigned_area();

        // 5. Calculate the convexity ratio
        let convexity_ratio_f64 = if hull_area_f64 > 1e-9 {
            (path_area_f64 / hull_area_f64).min(1.0) // Cap at 1.0
        } else {
            1.0
        };

        let convexity_ratio = Decimal::from_f64(convexity_ratio_f64)
            .ok_or_else(|| anyhow::anyhow!("Failed to convert convexity_ratio_f64 to Decimal"))?
            .max(Decimal::ZERO)
            .min(Decimal::ONE);

        debug!(
            "MANDORLA GAUGE: Convexity calculation | Path area: {:.6} | Hull area: {:.6} | Ratio: {:.4}",
            path_area_f64, hull_area_f64, convexity_ratio
        );

        Ok(convexity_ratio)
    }

    /// Calculates the Harmonic Path Score, replacing the old centroid bias.
    /// This score measures the path's alignment with high-liquidity, central assets,
    /// reflecting the stability and significance of the arbitrage opportunity.
    fn calculate_harmonic_path_score(&self, data: &[PoolGeometricData]) -> Result<Decimal> {
        let mut total_liquidity = Decimal::ZERO;
        let mut anchor_asset_weight = Decimal::ZERO;

        for pool_data in data.iter() {
            total_liquidity += pool_data.liquidity_usd;

            let (token_a, token_b) = &pool_data.token_symbols;
            let has_anchor_asset = self.anchor_assets.contains(token_a) || self.anchor_assets.contains(token_b);
            
            if has_anchor_asset {
                anchor_asset_weight += pool_data.liquidity_usd;
            }
        }

        if total_liquidity.is_zero() {
            debug!("MANDORLA GAUGE: No liquidity in path, returning zero score");
            return Ok(Decimal::ZERO);
        }

        // The score is the ratio of anchor asset liquidity to total path liquidity.
        let score = anchor_asset_weight / total_liquidity;

        debug!(
            "MANDORLA GAUGE: Harmonic Path Score calculation | Anchor liquidity: {:.2} | Total liquidity: {:.2} | Score: {:.4}",
            anchor_asset_weight, total_liquidity, score
        );

        Ok(score.max(Decimal::ZERO).min(Decimal::ONE))
    }

    /// Calculates the liquidity centroid bias.
    /// A lower bias indicates the path is more centered around highly liquid, central assets.
    /// For simplicity, this is currently an inverse of the harmonic path score.
    fn calculate_liquidity_centroid_bias(&self, _data: &[PoolGeometricData], harmonic_path_score: Decimal) -> Result<Decimal> {
        // A higher harmonic_path_score means more central assets, so bias should be lower.
        Ok(Decimal::ONE - harmonic_path_score)
    }
}


#[cfg(test)]
mod real_geometric_scorer_tests {
    use super::*;
    use ethers::types::Address;

    fn create_test_pool(reserve0: f64, reserve1: f64, token0: &str, token1: &str) -> ArbitragePool {
        ArbitragePool {
            address: Address::zero(),
            reserve0: Decimal::from_f64(reserve0).unwrap(),
            reserve1: Decimal::from_f64(reserve1).unwrap(),
            token0_symbol: token0.to_string(),
            token1_symbol: token1.to_string(),
            protocol: "Test".to_string(),
        }
    }

    #[tokio::test]
    async fn test_real_geometric_scorer_basic() {
        // Create a mock price oracle for testing
        use crate::data::oracle::MockPriceOracle;
        let mock_oracle = MockPriceOracle::new();
        let scorer = RealGeometricScorer::new(mock_oracle);
        
        let path = vec![
            create_test_pool(1000.0, 2000.0, "WETH", "USDC"),
            create_test_pool(1500.0, 1800.0, "WETH", "USDT"),
            create_test_pool(1200.0, 2200.0, "USDC", "DAI"),
        ];

        let score = scorer.calculate_score(&path).await.unwrap();
        
        // Should have reasonable scores
        assert!(score.convexity_ratio >= Decimal::ZERO);
        assert!(score.convexity_ratio <= Decimal::ONE);
        assert!(score.liquidity_centroid_bias >= Decimal::ZERO);
        assert!(score.liquidity_centroid_bias <= Decimal::ONE);
    }

    #[tokio::test]
    async fn test_real_geometric_scorer_anchor_assets() {
        // Create a mock price oracle for testing with realistic prices
        use crate::data::oracle::MockPriceOracle;
        use rust_decimal_macros::dec;
        
        let mut mock_oracle = MockPriceOracle::new();
        // Set up realistic prices for anchor assets
        mock_oracle.set_price(Address::zero(), dec!(2000.0)); // Default price for all tokens
        
        let scorer = RealGeometricScorer::new(mock_oracle);
        
        // Path with all anchor assets (should have low bias)
        let anchor_path = vec![
            create_test_pool(1000.0, 2000.0, "WETH", "USDC"),
            create_test_pool(1500.0, 1800.0, "WETH", "USDT"),
            create_test_pool(1200.0, 2200.0, "USDC", "DAI"),
        ];

        // Path with no anchor assets (should have high bias)
        let non_anchor_path = vec![
            create_test_pool(1000.0, 2000.0, "PEPE", "DOGE"),
            create_test_pool(1500.0, 1800.0, "SHIB", "FLOKI"),
            create_test_pool(1200.0, 2200.0, "MEME", "WIF"),
        ];

        let anchor_score = scorer.calculate_score(&anchor_path).await.unwrap();
        let non_anchor_score = scorer.calculate_score(&non_anchor_path).await.unwrap();

        // AUDIT-FIX: Anchor path should have lower bias than non-anchor path
        // (Lower bias means more central/stable, which anchor assets should be)
        assert!(
            anchor_score.liquidity_centroid_bias < non_anchor_score.liquidity_centroid_bias,
            "Anchor assets should have lower liquidity centroid bias (more central). Anchor: {:.3}, Non-anchor: {:.3}",
            anchor_score.liquidity_centroid_bias,
            non_anchor_score.liquidity_centroid_bias
        );
    }

    #[tokio::test]
    async fn test_real_geometric_scorer_short_path() {
        // Create a mock price oracle for testing
        use crate::data::oracle::MockPriceOracle;
        let mock_oracle = MockPriceOracle::new();
        let scorer = RealGeometricScorer::new(mock_oracle);
        
        // Path with only 2 pools (too short for meaningful geometric analysis)
        let short_path = vec![
            create_test_pool(1000.0, 2000.0, "WETH", "USDC"),
            create_test_pool(1500.0, 1800.0, "WETH", "USDT"),
        ];

        let score = scorer.calculate_score(&short_path).await.unwrap();
        
        // Should return neutral scores
        assert_eq!(score.convexity_ratio, dec!(0.5));
        assert_eq!(score.liquidity_centroid_bias, dec!(0.5));
    }
}

