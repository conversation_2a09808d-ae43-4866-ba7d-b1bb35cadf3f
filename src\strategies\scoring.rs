// src/strategies/scoring.rs

use crate::config::ScoringConfig;
use crate::shared_types::{AethericResonanceScore, GeometricScore, MarketCharacter, MarketRegime, NetworkResonanceState, TemporalHarmonics, Opportunity, ArbitragePath, ArbitragePool, GeometricScorer};
use rust_decimal::Decimal;
use rust_decimal_macros::dec;
use tracing::{debug, warn};
use std::sync::Arc;
use std::collections::HashMap;
use num_traits::FromPrimitive;
use anyhow::Result;

pub struct ScoringEngine {
    pub config: ScoringConfig,
    pub geometric_scorer: Arc<dyn GeometricScorer>,
}

impl ScoringEngine {
    pub fn new(config: ScoringConfig, geometric_scorer: Arc<dyn GeometricScorer>) -> Self {
        Self { config, geometric_scorer }
    }

    /// Calculates a comprehensive score for a given arbitrage opportunity.
    /// This score represents the risk-adjusted, regime-aware profit expectation.
    /// STRATEGIC-FIX: Implements proper three-pillar synthesis with weighted additive model
    /// AUDIT-FIX: Uses configured weights and neutral fallback values to prevent zero-out problem.
    pub async fn calculate_opportunity_score(
        &self,
        opportunity: &Opportunity,
        market_regime: &MarketRegime,
        latest_temporal_harmonics: &Option<TemporalHarmonics>,
        latest_network_resonance_state: &Option<NetworkResonanceState>,
        centrality_scores: &Arc<HashMap<String, Decimal>>,
    ) -> Decimal {
        // Step 1: Pre-flight quality check (Mandorla Gauge)
        let quality_ratio = opportunity.base().intersection_value_usd / opportunity.base().estimated_gross_profit_usd;
        if quality_ratio < self.config.quality_ratio_floor {
            debug!("Opportunity rejected: quality ratio {:.3} below floor {:.3}",
                   quality_ratio, self.config.quality_ratio_floor);
            return dec!(-1.0); // Reject immediately if quality is too low
        }

        // Step 2: Calculate Certainty-Equivalent Profit (Risk-Adjusted Profit)
        // AUDIT-FIX: Add bounds checking to prevent negative certainty equivalent
        let risk_adjustment = self.config.risk_aversion_k
            * opportunity.base().associated_volatility
            * opportunity.base().estimated_gross_profit_usd;
        let certainty_equivalent = (opportunity.base().estimated_gross_profit_usd - risk_adjustment)
            .max(dec!(0.0)); // Ensure non-negative

        // Step 3: Apply Regime Multiplier
        let regime_multiplier = self.get_regime_multiplier(market_regime);

        // Step 4: Calculate Temporal Harmonics Score (Chronos Sieve)
        let temporal_score = self.calculate_temporal_score(latest_temporal_harmonics);

        // Step 5: Calculate Geometric Score (Mandorla Gauge)
        let geometric_score = self.calculate_geometric_score(opportunity).await;

        // Step 6: Calculate Network Resonance Score (Network Seismology)
        let network_score = self.calculate_network_score(latest_network_resonance_state);

        // Step 7: STRATEGIC-FIX: Three-pillar synthesis using weighted additive model
        // This implements the true Zen Geometer philosophy where each pillar contributes
        // proportionally to the final Aetheric Resonance Score
        let weighted_pillar_score = (temporal_score * self.config.temporal_harmonics_weight)
            + (geometric_score * self.config.geometric_score_weight)
            + (network_score * self.config.network_resonance_weight);

        // Step 8: Apply regime multiplier to final score
        let final_score = certainty_equivalent * regime_multiplier * weighted_pillar_score;

        debug!(
            "ZEN GEOMETER: Three-Pillar Synthesis Complete - Final Score {:.4} = CertaintyEquivalent {:.2} * RegimeMultiplier {:.2} * WeightedPillars[Temporal {:.2}*{:.2} + Geometric {:.2}*{:.2} + Network {:.2}*{:.2}]",
            final_score,
            certainty_equivalent,
            regime_multiplier,
            temporal_score, self.config.temporal_harmonics_weight,
            geometric_score, self.config.geometric_score_weight,
            network_score, self.config.network_resonance_weight
        );

        final_score
    }

    /// STRATEGIC-FIX: Calculate temporal harmonics score with proper Chronos Sieve analysis
    /// AUDIT-FIX: Uses neutral fallback instead of zero to prevent score elimination
    fn calculate_temporal_score(&self, temporal_harmonics: &Option<TemporalHarmonics>) -> Decimal {
        match temporal_harmonics.as_ref() {
            Some(harmonics) => {
                // STRATEGIC-FIX: Use market rhythm stability as primary temporal indicator
                // This represents the Chronos Sieve's assessment of market cycle coherence
                let stability_score = Decimal::from_f64(harmonics.market_rhythm_stability)
                    .unwrap_or(dec!(0.5)); // Neutral fallback

                // TODO: Future enhancement - incorporate dominant_cycles_minutes and wavelet_features
                // for more sophisticated temporal analysis as per audit recommendations
                
                debug!("CHRONOS SIEVE: Market rhythm stability {:.3}", stability_score);
                stability_score.max(dec!(0.0)).min(dec!(1.0)) // Clamp to [0,1]
            }
            None => {
                debug!("CHRONOS SIEVE: Temporal harmonics unavailable, using neutral score");
                dec!(0.5) // AUDIT-FIX: Neutral score instead of zero prevents score elimination
            }
        }
    }

    /// STRATEGIC-FIX: Calculate geometric score using complete Mandorla Gauge analysis
    /// AUDIT-FIX: Uses ALL three components and neutral fallback to prevent score elimination
    async fn calculate_geometric_score(&self, opportunity: &Opportunity) -> Decimal {
        let arbitrage_path = self.opportunity_to_arbitrage_path(opportunity);
        match self.geometric_scorer.calculate_score(&arbitrage_path).await {
            Ok(score) => {
                // STRATEGIC-FIX: Use ALL three components of the Mandorla Gauge
                // This represents the complete geometric analysis of opportunity structure
                let combined_score = (score.convexity_ratio
                    + score.liquidity_centroid_bias
                    + score.harmonic_path_score) / dec!(3.0);
                
                debug!(
                    "MANDORLA GAUGE: Geometric analysis complete - Convexity: {:.3}, Centroid: {:.3}, Harmonic: {:.3}, Combined: {:.3}",
                    score.convexity_ratio, score.liquidity_centroid_bias, score.harmonic_path_score, combined_score
                );
                
                combined_score.max(dec!(0.0)).min(dec!(1.0)) // Clamp to [0,1]
            }
            Err(e) => {
                warn!("MANDORLA GAUGE: Failed to calculate geometric score: {}", e);
                dec!(0.5) // AUDIT-FIX: Neutral fallback instead of zero prevents score elimination
            }
        }
    }

    /// STRATEGIC-FIX: Calculate network resonance score with proper Network Seismology analysis
    /// AUDIT-FIX: Uses neutral fallback instead of zero to prevent score elimination
    fn calculate_network_score(&self, network_state: &Option<NetworkResonanceState>) -> Decimal {
        match network_state.as_ref() {
            Some(state) => {
                // STRATEGIC-FIX: Use network coherence as primary network health indicator
                // This represents the Network Seismology assessment of blockchain medium state
                let coherence_score = Decimal::from_f64(state.network_coherence_score)
                    .unwrap_or(dec!(0.5)); // Neutral fallback

                // Apply penalties for shock events (network stress indicators)
                let shock_penalty = if state.is_shock_event { dec!(0.8) } else { dec!(1.0) };

                let final_score = coherence_score * shock_penalty;
                
                debug!(
                    "NETWORK SEISMOLOGY: Coherence {:.3}, Shock Event: {}, S-P Time: {:.1}ms, Final Score: {:.3}",
                    coherence_score, state.is_shock_event, state.sp_time_ms, final_score
                );
                
                final_score.max(dec!(0.0)).min(dec!(1.0)) // Clamp to [0,1]
            }
            None => {
                debug!("NETWORK SEISMOLOGY: Network resonance state unavailable, using neutral score");
                dec!(0.5) // AUDIT-FIX: Neutral score instead of zero prevents score elimination
            }
        }
    }

    /// Helper function to get regime-specific multiplier
    fn get_regime_multiplier(&self, regime: &MarketRegime) -> Decimal {
        // During a gas war, we heavily penalize everything. Only exceptional opps will pass.
        if *regime == MarketRegime::BotGasWar {
            return self.config.regime_multiplier_gas_war_penalty;
        }

        match regime {
            MarketRegime::RetailFomoSpike => self.config.regime_multiplier_retail_fomo,
            MarketRegime::HighVolatilityCorrection => self.config.regime_multiplier_high_vol,
            MarketRegime::CalmOrderly => self.config.regime_multiplier_calm,
            _ => dec!(1.0), // Default multiplier
        }
    }

    /// Convert an opportunity to an arbitrage path for geometric analysis
    pub fn opportunity_to_arbitrage_path(&self, opportunity: &Opportunity) -> ArbitragePath {
        // Extract pools from the opportunity and convert to ArbitragePool format
        let pools = opportunity.get_pools();
        
        pools.iter().map(|pool| {
            ArbitragePool {
                address: pool.address,
                reserve0: pool.reserve_0,
                reserve1: pool.reserve_1,
                token0_symbol: pool.token_0.clone(),
                token1_symbol: pool.token_1.clone(),
                protocol: match pool.protocol {
                    crate::shared_types::ProtocolType::UniswapV2 => "Uniswap V2".to_string(),
                    crate::shared_types::ProtocolType::UniswapV3 => "Uniswap V3".to_string(),
                    crate::shared_types::ProtocolType::SushiSwap => "SushiSwap".to_string(),
                    crate::shared_types::ProtocolType::PancakeSwap => "PancakeSwap".to_string(),
                    crate::shared_types::ProtocolType::Other(ref name) => name.clone(),
                },
            }
        }).collect()
    }
}
